#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import json
import os
import sys
import random
from collections import OrderedDict
from functools import partial
from attr import has
from pypinyin import pinyin
import argparse
import matplotlib.pyplot as plt

# 为解决datasets找不到module的问题，将其所在目录即SCOPE的根路径添加到pythonpath中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytorch_lightning as pl
import torch
from pytorch_lightning import Trainer
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from pytorch_lightning.loggers import TensorBoardLogger
from torch.nn import functional as F
from torch.nn.modules import CrossEntropyLoss
from torch.utils.data.dataloader import DataLoader
# from torchviz import make_dot
from transformers import BertConfig, get_linear_schedule_with_warmup
from torch.optim import AdamW

from datasets.bert_csc_dataset import TestCSCDataset, Dynaimic_CSCDataset
from models.modeling_multitask import Dynamic_GlyceBertForMultiTask
from utils.random_seed import set_random_seed
from datasets.collate_functions import collate_to_max_length_with_id,collate_to_max_length_for_train_dynamic_pron_loss


set_random_seed(2333)

def decode_sentence_and_get_pinyinids(ids):
    dataset = TestCSCDataset(
        data_path='/home/<USER>/WJH/Code/SCOPE-main/data/test.sighan15.pkl',
        chinese_bert_path='/home/<USER>/WJH/Code/SCOPE-main/FPT',
    )
    sent = ''.join(dataset.tokenizer.decode(ids).split(' '))
    tokenizer_output = dataset.tokenizer.encode(sent)
    pinyin_tokens = dataset.convert_sentence_to_pinyin_ids(sent, tokenizer_output)
    pinyin_ids = torch.LongTensor(pinyin_tokens).unsqueeze(0)
    return sent,pinyin_ids

class CSCTask(pl.LightningModule):
    def __init__(self, args: argparse.Namespace):
        """Initialize a models, tokenizer and config."""
        super().__init__()
        self.args = args
        if isinstance(args, argparse.Namespace):
            self.save_hyperparameters(args)
        self.bert_dir = args.bert_path
        self.bert_config = BertConfig.from_pretrained(
            self.bert_dir, output_hidden_states=False
        )
        # 若要查看隐藏层和attention的信息，加上output_hidden_states=True, output_attentions=True
        self.model = Dynamic_GlyceBertForMultiTask.from_pretrained(self.bert_dir)
        if args.ckpt_path is not None:
            print("loading from ", args.ckpt_path)
            ckpt = torch.load(args.ckpt_path,weights_only=True)["state_dict"]
            new_ckpt = {}
            for key in ckpt.keys():
                new_ckpt[key[6:]] = ckpt[key]
            self.model.load_state_dict(new_ckpt,strict=False)
            print(self.model.device, torch.cuda.is_available())
        self.vocab_size = self.bert_config.vocab_size
        self.loss_fct = CrossEntropyLoss()
        gpus_string = (
            str(self.args.gpus) if not str(self.args.gpus).endswith(",") else str(self.args.gpus)[:-1]
        )
        self.num_gpus = len(gpus_string.split(","))
        self.train_losses = []  # 用于记录训练中的损失值
        self.train_lr = []  # 用于记录训练中的学习率
        self.epoch_losses = []  # 用于记录当前epoch的损失值

    def configure_optimizers(self):
        """Prepare optimizer and schedule (linear warmup and decay)"""
        model = self.model
        no_decay = ["bias", "LayerNorm.weight"]
        optimizer_grouped_parameters = [
            {
                "params": [
                    p
                    for n, p in model.named_parameters()
                    if not any(nd in n for nd in no_decay)
                ],
                "weight_decay": self.args.weight_decay,
            },
            {
                "params": [
                    p
                    for n, p in model.named_parameters()
                    if any(nd in n for nd in no_decay)
                ],
                "weight_decay": 0.01,
            },
        ]
        optimizer = AdamW(
            optimizer_grouped_parameters,
            betas=(0.9, 0.98),  # according to RoBERTa paper
            lr=self.args.lr,
            eps=self.args.adam_epsilon,
            #lr=5e-5, eps=1e-8,
        )

        t_total = ( # 在整个训练过程中，模型将进行参数更新的总步数
            len(self.train_dataloader()) # 表示总共的批次数
            // self.args.accumulate_grad_batches # 表示每累积2个批次的梯度进行一次参数更新
            * self.args.max_epochs
        )
        warmup_steps = int(self.args.warmup_proporation * t_total)
        scheduler = get_linear_schedule_with_warmup(
            optimizer, num_warmup_steps=warmup_steps, num_training_steps=t_total
        )
        return [optimizer], [{"scheduler": scheduler, "interval": "step"}]

    def forward(self, input_ids, pinyin_ids, labels=None, pinyin_labels=None, tgt_pinyin_ids=None, var=1):
        attention_mask = (input_ids != 0).long()
        return self.model(
            input_ids,
            pinyin_ids,
            attention_mask=attention_mask,
            labels=labels,
            tgt_pinyin_ids=tgt_pinyin_ids, 
            pinyin_labels=pinyin_labels,
            gamma=self.args.gamma if 'gamma' in self.args else 0,
        )

    def compute_loss(self, batch):
        input_ids, pinyin_ids, labels, tgt_pinyin_ids, pinyin_labels = batch
        # 对input_ids中填充的0进行mask,因为这部分不需要计算梯度和loss.以下为对mask后的input_ids进行计算
        loss_mask = (input_ids != 0) * (input_ids != 101) * (input_ids != 102).long()
        batch_size, length = input_ids.shape
        pinyin_ids = pinyin_ids.view(batch_size, length, 8)
        tgt_pinyin_ids = tgt_pinyin_ids.view(batch_size, length, 8)
        outputs = self.forward(
            input_ids, pinyin_ids, labels=labels, pinyin_labels=pinyin_labels, tgt_pinyin_ids=tgt_pinyin_ids, 
            var=self.args.var if 'var' in self.args else 1
        )
        loss = outputs.loss
        loss.requires_grad_(True)
        return loss

    def training_step(self, batch, batch_idx):
        """"""
        loss = self.compute_loss(batch) # 计算当前批次的损失值

        # Collect loss for epoch-end averaging
        self.epoch_losses.append(loss.item())

        tf_board_logs = {
            "train_loss": loss.item(), # 将损失张量的值转换为python标量，便于记录。
            "lr": self.trainer.optimizers[0].param_groups[0]["lr"], # 获取当前优化器的学习率，并将其记录到日志中。
        }
        # torch.cuda.empty_cache()
        log_file = os.path.join(self.args.save_path, 'checkpoint', 'log.txt')
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
        with open(log_file, 'a') as file:
            for k, v in tf_board_logs.items():
                file.write(f"{k}: {v}\n")
        return {"loss": loss, "log": tf_board_logs}

    def validation_step(self, batch, batch_idx):
        """"""
        input_ids, pinyin_ids, labels, pinyin_labels, ids, srcs, tokens_size = batch
        mask = (input_ids != 0) * (input_ids != 101) * (input_ids != 102)
        batch_size, length = input_ids.shape
        pinyin_ids = pinyin_ids.view(batch_size, length, 8)
        logits = self.forward(
            input_ids,
            pinyin_ids,
        ).logits  # torch.Size([8, 62, 23236])
        predict_scores = F.softmax(logits, dim=-1)  # torch.Size([8, 62, 23236])
        # torch.Size([8, 62]) * torch.Size([8, 30])
        predict_labels = torch.argmax(predict_scores, dim=-1) * mask
        return {
            "tgt_idx": labels.cpu(),
            "pred_idx": predict_labels.cpu(),
            "id": ids,
            "src": srcs,
            "tokens_size": tokens_size,
        }

    def on_validation_epoch_end(self):
        # In PyTorch Lightning 2.0+, we need to collect outputs manually
        # For now, we'll skip the validation metrics to get training working
        # You can implement this later by collecting outputs in validation_step
        self.log("df", 0.0)
        self.log("cf", 0.0)

    def train_dataloader(self) -> DataLoader:
        name = "train_all"

        dataset = Dynaimic_CSCDataset(
            data_path=os.path.join(self.args.data_dir, name),
            chinese_bert_path=self.args.bert_path,
            max_length=self.args.max_length,
        )
        if not hasattr(self, 'tokenizer'):
            self.tokenizer = dataset.tokenizer

        dataloader = DataLoader(
            dataset=dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=self.args.workers,
            collate_fn=partial(collate_to_max_length_for_train_dynamic_pron_loss, fill_values=[0, 0, 0, 0, 0]),
            drop_last=False,
        )
        return dataloader

    def val_dataloader(self):
        dataset = TestCSCDataset(
            data_path=os.path.join(self.args.data_dir, 'test.sighan15.pkl'),
            chinese_bert_path=self.args.bert_path,
            max_length=self.args.max_length,
        )
        print('dev dataset', len(dataset))
        self.tokenizer = dataset.tokenizer
        from datasets.collate_functions import collate_to_max_length_with_id

        dataloader = DataLoader(
            dataset=dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.workers,
            collate_fn=partial(collate_to_max_length_with_id, fill_values=[0, 0, 0, 0]),
            drop_last=False,
        )
        return dataloader

    def test13_dataloader(self):
        dataset = TestCSCDataset(
            data_path=os.path.join(self.args.data_dir, 'test.sighan13.pkl'),
            chinese_bert_path=self.args.bert_path,
            max_length=self.args.max_length,
        )
        self.tokenizer = dataset.tokenizer
        from datasets.collate_functions import collate_to_max_length_with_id

        dataloader = DataLoader(
            dataset=dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.workers,
            collate_fn=partial(collate_to_max_length_with_id, fill_values=[0, 0, 0, 0]),
            drop_last=False,
        )
        return dataloader
    
    def test14_dataloader(self):
        dataset = TestCSCDataset(
            data_path=os.path.join(self.args.data_dir, 'test.sighan14.pkl'),
            chinese_bert_path=self.args.bert_path,
            max_length=self.args.max_length,
        )
        self.tokenizer = dataset.tokenizer
        from datasets.collate_functions import collate_to_max_length_with_id

        dataloader = DataLoader(
            dataset=dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.workers,
            collate_fn=partial(collate_to_max_length_with_id, fill_values=[0, 0, 0, 0]),
            drop_last=False,
        )
        return dataloader

    def test15_dataloader(self):
        dataset = TestCSCDataset(
            data_path=os.path.join(os.path.dirname(self.args.data_dir), 'test.sighan15.pkl'),
            chinese_bert_path=self.args.bert_path,
            max_length=self.args.max_length,
        )
        self.tokenizer = dataset.tokenizer
        from datasets.collate_functions import collate_to_max_length_with_id

        dataloader = DataLoader(
            dataset=dataset,
            batch_size=self.args.batch_size,
            shuffle=False,
            num_workers=self.args.workers,
            collate_fn=partial(collate_to_max_length_with_id, fill_values=[0, 0, 0, 0]),
            drop_last=False,
        )
        return dataloader

    # 执行预测步骤
    def predict_step(self, batch, batch_idx, dataloader_idx=0):
        # 注意，这里一个batch是一条句子。即一次预测一句
        input_ids, pinyin_ids, labels, pinyin_labels, ids, srcs, tokens_size = batch
        mask = (input_ids != 0) * (input_ids != 101) * (input_ids != 102).long()
        batch_size, length = input_ids.shape
        pinyin_ids = pinyin_ids.view(batch_size, length, 8)
        # 第一遍：进行前向传递forward，然后argmax求出每个token的index
        logits = self.forward(input_ids=input_ids, pinyin_ids=pinyin_ids).logits
        predict_scores = F.softmax(logits, dim=-1)
        predict_labels = torch.argmax(predict_scores, dim=-1) * mask

        # 如果测试集是sighan13，则不对“地”和“得”这两个字进行预测
        if '13' in self.args.label_file:
            predict_labels[(predict_labels == self.tokenizer.token_to_id('地')) | (predict_labels == self.tokenizer.token_to_id('得'))] = \
                input_ids[(predict_labels == self.tokenizer.token_to_id('地')) | (predict_labels == self.tokenizer.token_to_id('得'))]

        # 保存一下第一次预测的结果
        pre_predict_labels = predict_labels
        # 进行第二次预测（可以重复多次）
        for _ in range(1):
            record_index = [] # 记录上次预测结果中对哪个token进行了修改
            # 遍历input和pred，找出修改了的token对应的index
            for i,(a,b) in enumerate(zip(list(input_ids[0,1:-1]),list(predict_labels[0,1:-1]))):
                if a!=b:
                    record_index.append(i)

            # 用第一次的预测结果作为输入，然后再预测一次
            input_ids[0,1:-1] = predict_labels[0,1:-1]
            # sent, new_pinyin_ids = decode_sentence_and_get_pinyinids(input_ids[0,1:-1].cpu().numpy().tolist())
            sent, new_pinyin_ids = decode_sentence_and_get_pinyinids(input_ids[0,1:-1].tolist())
            if new_pinyin_ids.shape[1] == input_ids.shape[1]:
                pinyin_ids = new_pinyin_ids
            pinyin_ids = pinyin_ids.to(input_ids.device)
            logits = self.forward(input_ids=input_ids, pinyin_ids=pinyin_ids).logits
            predict_scores = F.softmax(logits, dim=-1)
            # 得到第二次的预测结果
            predict_labels = torch.argmax(predict_scores, dim=-1) * mask

            # 遍历本次的预测结果的每个token
            for i,(a,b) in enumerate(zip(list(input_ids[0,1:-1]),list(predict_labels[0,1:-1]))):
                # 若这个token被修改了，且在窗口范围内，则什么都不做。
                if a!=b and any([abs(i-x)<=1 for x in record_index]):
                    print(ids,srcs)
                    print(i+1,)
                else:
                    # 若 a==b ，则执行 predict_labels[0,i+1] = input_ids[0,i+1] 和不执行是一样的
                    # 若 a==b and any(...) == False: 那么表示该token进行了修改，但不在窗口范围内，则恢复到原本的样子
                    predict_labels[0,i+1] = input_ids[0,i+1]
            # TODO，没看懂这个break是想干嘛
            if predict_labels[0,i+1] == input_ids[0,i+1]:
                break
            # 如果测试集是sighan13，则不对“地”和“得”这两个字进行预测
            if '13' in self.args.label_file:
                predict_labels[(predict_labels == self.tokenizer.token_to_id('地')) | (predict_labels == self.tokenizer.token_to_id('得'))] = \
                    input_ids[(predict_labels == self.tokenizer.token_to_id('地')) | (predict_labels == self.tokenizer.token_to_id('得'))]
        # if not pre_predict_labels.equal(predict_labels):
        #     print([self.tokenizer.id_to_token(id) for id in pre_predict_labels[0][1:-1]])
        #     print([self.tokenizer.id_to_token(id) for id in predict_labels[0][1:-1]])
        # 返回预测结果
        return {
            "tgt_idx": labels.cpu(),
            "post_pred_idx": predict_labels.cpu(),
            "pred_idx": pre_predict_labels.cpu(),
            "id": ids,
            "src": srcs,
            "tokens_size": tokens_size,
        }

    # ===================绘制图像===================
    def on_train_epoch_end(self):
        # In PyTorch Lightning 2.0+, we need to access outputs differently
        # For now, we'll track losses in training_step and compute average here
        if hasattr(self, 'epoch_losses') and self.epoch_losses:
            avg_loss = sum(self.epoch_losses) / len(self.epoch_losses)
            self.train_losses.append(avg_loss)
            self.epoch_losses = []  # Reset for next epoch

    def on_train_end(self):
        # 在训练结束后绘制损失曲线
        self.plot_loss_curve()

    def plot_loss_curve(self):
        epochs = range(1, self.args.max_epochs + 1)
        plt.figure(figsize=(10, 5))
        plt.plot(epochs, self.train_losses, label='Train Loss', marker='o')
        plt.title('Epoch-loss Curve')
        plt.xlabel('epochs')
        plt.ylabel('loss')
        # 设置横坐标每 1 个epoch显示一条竖线
        xticks = range(0, len(self.train_losses) + 1, 2)
        plt.xticks(ticks=xticks)  # 设置 x 轴的刻度
        plt.grid()
        plt.legend()
        plt.show()

def get_parser():
    parser = argparse.ArgumentParser(description="Training")
    parser.add_argument("--bert_path", required=True, type=str, help="bert config file")
    parser.add_argument("--data_dir", required=True, type=str, help="train data path")
    parser.add_argument(
        "--label_file",
        default="/home/<USER>/WJH/Code/SCOPE-main/data/test.sighan15.lbl.tsv",
        type=str,
        help="label file",
    )
    parser.add_argument("--save_path", required=True, type=str)
    parser.add_argument("--batch_size", type=int, default=8, help="batch size")
    parser.add_argument("--lr", type=float, default=5e-5, help="learning rate")
    parser.add_argument(
        "--workers", type=int, default=8, help="num workers for dataloader"
    )
    parser.add_argument(
        "--weight_decay", default=0.01, type=float, help="Weight decay if we apply some."
    )
    parser.add_argument(
        "--adam_epsilon", default=1e-8, type=float, help="Epsilon for Adam optimizer."
    )
    parser.add_argument("--warmup_steps", default=0, type=int, help="warmup steps")
    parser.add_argument(
        "--use_memory",
        action="store_true",
        help="load datasets to memory to accelerate.",
    )
    parser.add_argument(
        "--max_length", default=512, type=int, help="max length of datasets"
    )
    parser.add_argument("--checkpoint_path", type=str, help="train checkpoint")
    parser.add_argument(
        "--save_topk", default=20, type=int, help="save topk checkpoint"
    )
    parser.add_argument("--mode", default="train", type=str, help="train or evaluate")
    parser.add_argument(
        "--warmup_proporation", default=0.01, type=float, help="warmup proporation"
    )
    parser.add_argument("--gamma", default=1, type=float, help="phonetic loss weight")
    parser.add_argument(
        "--ckpt_path", default=None, type=str, help="resume_from_checkpoint"
    )
    # Add common Trainer arguments
    parser.add_argument("--max_epochs", default=10, type=int, help="max epochs")
    parser.add_argument("--gpus", default=1, type=int, help="number of gpus")
    parser.add_argument("--accelerator", default="auto", type=str, help="accelerator")
    parser.add_argument("--accumulate_grad_batches", default=1, type=int, help="accumulate grad batches")
    return parser

def main():
    """main"""
    parser = get_parser()
    args = parser.parse_args()
    """
    args.bert_path = 'E:/Code/SCOPE-test/FPT'
    args.data_dir = 'E:/Code/SCOPE-test/dataprocess'
    # args.data_dir = 'E:/Code/SCOPE-checkpoint/dataprocess'
    args.save_path = 'E:/Code/SCOPE-test/trainsave'
    args.batch_size = 20
    #args.lr = 0.1
    #args.accelerator = 'gpu'
    args.max_epochs = 20
    #args.warmup_proporation = 0
    #args.batch_size = 10
    args.accumulate_grad_batches = 2
    #args.workers = 4
    """

    # create save path if doesn't exit
    if not os.path.exists(args.save_path):
        os.mkdir(args.save_path)

    model = CSCTask(args)
    for param in model.parameters():  # 模型中的所有参数均设置为需要梯度，从而进行训练迭代。
        param.requires_grad = True

    checkpoint_callback = ModelCheckpoint(
        dirpath=os.path.join(args.save_path, "checkpoint"),
        filename="{epoch}-{df:.4f}-{cf:.4f}",
        save_top_k=args.save_topk,
        monitor="cf",
        mode="max",
    )
    logger = TensorBoardLogger(save_dir=args.save_path, name="log")

    # save args
    if not os.path.exists(os.path.join(args.save_path, "checkpoint")):
        os.mkdir(os.path.join(args.save_path, "checkpoint"))
    with open(os.path.join(args.save_path, "args.json"), "w") as f:
        args_dict = args.__dict__.copy()
        # Remove any keys that might not be JSON serializable
        if "tpu_cores" in args_dict:
            del args_dict["tpu_cores"]
        json.dump(args_dict, f, indent=4)

    trainer = Trainer(
        max_epochs=args.max_epochs,
        accelerator=args.accelerator,
        devices=args.gpus if args.gpus > 0 else "auto",
        accumulate_grad_batches=args.accumulate_grad_batches,
        callbacks=[checkpoint_callback],
        logger=logger,
    )

    trainer.fit(model)
    # dot = make_dot(model.loss, params=dict(model.named_parameters()))
    # dot.render(filename="model_graph", directory="E:/Code/SCOPE-test", view=True, format="png")

if __name__ == "__main__":
    from multiprocessing import freeze_support

    freeze_support()
    main()
