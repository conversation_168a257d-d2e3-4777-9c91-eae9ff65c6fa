#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess
import sys

def test_training():
    """Test the training script with minimal parameters"""
    
    # Set up paths
    current_dir = "/home/<USER>/WJH/Code/SCOPE-main"
    bert_path = os.path.join(current_dir, "FPT")
    data_dir = os.path.join(current_dir, "data")
    save_path = os.path.join(current_dir, "test_output")
    
    # Create output directory
    os.makedirs(save_path, exist_ok=True)
    
    # Check if required files exist
    required_files = [
        os.path.join(bert_path, "config.json"),
        os.path.join(bert_path, "pytorch_model.bin"),
        os.path.join(data_dir, "train_all"),
        os.path.join(data_dir, "test.sighan15.pkl"),
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    # Prepare training command
    cmd = [
        sys.executable, "finetune/train.py",
        "--bert_path", bert_path,
        "--data_dir", data_dir,
        "--save_path", save_path,
        "--batch_size", "2",  # Small batch size for testing
        "--max_epochs", "1",  # Just 1 epoch for testing
        "--lr", "5e-5",
        "--max_length", "128",  # Shorter sequences for faster testing
        "--gpus", "1" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "0",
        "--accelerator", "gpu" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu",
    ]
    
    print("Starting training test...")
    print("Command:", " ".join(cmd))
    
    # Set environment variables
    env = os.environ.copy()
    env["PYTHONPATH"] = current_dir
    
    try:
        # Run the training command
        result = subprocess.run(
            cmd,
            cwd=current_dir,
            env=env,
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("Training test completed successfully!")
            return True
        else:
            print(f"Training test failed with return code: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("Training test timed out after 5 minutes")
        return False
    except Exception as e:
        print(f"Error running training test: {e}")
        return False

if __name__ == "__main__":
    success = test_training()
    sys.exit(0 if success else 1)
