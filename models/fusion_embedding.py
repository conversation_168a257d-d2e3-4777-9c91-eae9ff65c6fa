#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@file  : glyce_embedding.py
@author: zi<PERSON>
@contact : <EMAIL>
@date  : 2020/8/23 10:40
@version: 1.0
@desc  : 【char embedding】+【pinyin embedding】+【glyph embedding】 = fusion embedding
"""
import os

import torch
from torch import nn

from models.glyph_embedding import GlyphEmbedding
from models.pinyin_embedding import PinyinEmbedding

from AttentionBlocks import PAMAttention_1D

class FusionBertEmbeddings(nn.Module):
    """
    Construct the embeddings from word, position, glyph, pinyin and token_type embeddings.
    """
    def __init__(self, config):
        super(FusionBertEmbeddings, self).__init__()
        config_path = os.path.join(config.name_or_path, 'config')
        font_files = []
        for file in os.listdir(config_path):
            if file.endswith(".npy"):
                font_files.append(os.path.join(config_path, file))
        # word_embeddings.weight.shape: torch.Size([23236, 768])  默认的config hidden_size=768
        self.word_embeddings = nn.Embedding(config.vocab_size, config.hidden_size, padding_idx=0)
        # position_embeddings.weight.shape: torch.Size([512, 768])
        self.position_embeddings = nn.Embedding(config.max_position_embeddings, config.hidden_size)
        # token_type_embeddings.weight.shape: torch.Size([2, 768])
        self.token_type_embeddings = nn.Embedding(config.type_vocab_size, config.hidden_size)
        # pinyin_embedding: (128, 768)
        self.pinyin_embeddings = PinyinEmbedding(embedding_size=128, pinyin_out_dim=config.hidden_size,
                                                 config_path=config_path)
        # glyph_embedding: (batch, sentence_length, 1728)
        self.glyph_embeddings = GlyphEmbedding(font_npy_files=font_files)

        # self.LayerNorm is not snake-cased to stick with TensorFlow models variable name and be able to load
        # any TensorFlow checkpoint file
        self.glyph_map = nn.Linear(1728, config.hidden_size)
        self.map_fc = nn.Linear(config.hidden_size * 3, config.hidden_size)
        self.map_wordembedding = nn.Linear(768, 640)  # 尝试将输入降维
        self.map_pamembedding = nn.Linear(640, 128)
        self.LayerNorm = nn.LayerNorm(config.hidden_size, eps=config.layer_norm_eps)
        self.dropout = nn.Dropout(config.hidden_dropout_prob)

        # position_ids (1, len position emb) is contiguous in memory and exported when serialized
        self.register_buffer("position_ids", torch.arange(config.max_position_embeddings).expand((1, -1)))

    def forward(self, input_ids=None, pinyin_ids=None, token_type_ids=None, position_ids=None, inputs_embeds=None):
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        if input_ids is not None:
            input_shape = input_ids.size()
        else:
            input_shape = inputs_embeds.size()[:-1]

        seq_length = input_shape[1]

        if position_ids is None:
            position_ids = self.position_ids[:, :seq_length]

        if token_type_ids is None:
            token_type_ids = torch.zeros(input_shape, dtype=torch.long, device=self.position_ids.device)

        if inputs_embeds is None:
            inputs_embeds = self.word_embeddings(input_ids)  # input_ids.size(): [8, 30]

        # get char embedding, pinyin embedding and glyph embedding
        word_embeddings = inputs_embeds  # [bs,l,hidden_size] [8, 30, 768]
        pinyin_embeddings = self.pinyin_embeddings(pinyin_ids)  # [bs,l,hidden_size]
        glyph_embeddings = self.glyph_map(self.glyph_embeddings(input_ids))  # [bs,l,hidden_size]
        # 添加全局语义变量GlobalSemanticEmbedding
        '''
        添加PAMAttention
        '''
        # (B,T,D)  N:序列的个数, T:时间序列的长度;
        # 维度变换
        word_downembedding = self.map_wordembedding(word_embeddings)  # 768-->640
        seq_length = word_downembedding.shape[1]  # seq_length = 40
        window_size = []
        if 2 <= seq_length < 8:
            window_size = [2, 2]
        elif 8 <= seq_length < 16:
            window_size = [2, 2, 2]
        elif 16 <= seq_length < 32:
            window_size = [2, 2, 2, 2]
        elif 32 <= seq_length < 64:
            window_size = [2, 2, 2, 2, 2]
        elif 64 <= seq_length < 128:
            window_size = [2, 2, 2, 2, 2, 2]
        elif 128 <= seq_length < 256:
            window_size = [2, 2, 2, 2, 2, 2, 2]

        # 得到金字塔的mask矩阵; 以输入序列长度等于30,三层卷积核分别为[2, 2, 2]为例子: all_size=[30,15,7,3],存放每一个尺度对应的序列长度
        mask, all_size = PAMAttention_1D.get_mask(input_size=seq_length, window_size=window_size, inner_size=3,
                                               device=device)
        # 通过卷积构造金字塔结构  (多尺度语义构造模块)
        conv_layers = PAMAttention_1D.Conv_Construct(d_model=640, seq_length=seq_length, window_size=window_size).to(
            device)
        # 定义多头注意力机制，需要注意维度对应，其中d_model=hidden_state=n_head*d_k
        Model = PAMAttention_1D.MultiHeadAttention(n_head=8, d_model=640, d_k=80, dropout=0.1, normalize_before=False,
                                                all_size=all_size).to(device)

        X = conv_layers(word_downembedding)  # 执行粗尺度构造模块(B,T,N,D)-->(B,M,N,D)
        pam_embedding = Model(X, X, X, mask=mask)  # 执行注意力机制： (B,M,N,D)--> (B,T,N,D)
        pam_embedding = self.map_pamembedding(pam_embedding)  # 640-->128
        print("pam_embedding.shape: ", pam_embedding.shape)
        #autoencoder = PAMAttention_1D.Autoencoder().to(device)
        #embedding_output_pam = autoencoder.encoder_256(embedding_output_pam)
        #print("sequence_output[pam].shape: ", sequence_output_pam.shape)
        #将降维后的sequence_output_rd和PAMAttention输出的sequence_output_pam进行concat，重新生成768维的向量
        #embedding_output = torch.cat((embedding_output_de, embedding_output_pam), dim=2)
        #print("sequence_output_final.shape: ", sequence_output.shape)

        word_embeddings = torch.cat((word_downembedding, pam_embedding), dim=2)

        # fusion layer  (8, 30, 768*3=2304) --> (8, 30, 768*4=3072)
        concat_embeddings = torch.cat((word_embeddings, pinyin_embeddings, glyph_embeddings), 2)
        inputs_embeds = self.map_fc(concat_embeddings)  # (8, 30, 768)

        position_embeddings = self.position_embeddings(position_ids)  # [1, 30, 768]
        token_type_embeddings = self.token_type_embeddings(token_type_ids)  # [1, 30, 768]

        embeddings = inputs_embeds + position_embeddings + token_type_embeddings  # (8, 30, 768)
        embeddings = self.LayerNorm(embeddings)  # (8, 30, 768)
        embeddings = self.dropout(embeddings)  # (8, 30, 768)
        return embeddings

if __name__ == '__main__':
    from models import modeling_multitask as mm
    from transformers import BertTokenizer
    bert_path = 'E:/Code/SCOPE-main/FPT'
    config = mm.Dynamic_GlyceBertForMultiTask.from_pretrained(bert_path).config
    b = FusionBertEmbeddings(config)
    a = BertTokenizer.from_pretrained(bert_path)
    text='我从来不乱花钱'
    #text = '因为从你到台湾留学的那天至今已过了半年我们就没机会像之前能经常见面的聊东聊西。'
    hanzi = a.tokenize(text)
    print("hanzi: ", hanzi)
    token_ids = a.encode(text, add_special_tokens=True)
    print("token_ids: ", token_ids)
    input_ids = torch.tensor(token_ids).unsqueeze(0)
    tok = b.word_embeddings(input_ids)
    vectors = tok[0]
    print("embedding shape: ", tok.shape)
    for i, vector in enumerate(vectors):
        print(f"Vector {i}: {vector}")

    '''
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    models = BertModel.from_pretrained('bert-base-uncased')
    input_ids = torch.tensor(tokenizer.encode("Hello, my dog is cute", add_special_tokens=True)).unsqueeze(
        0)  # Batch size 1
    outputs = models(input_ids)
    last_hidden_states = outputs[0]  # The last hidden-state is the first element of the output tuple
    
    a=ChineseBertDataset(None,'E:/Code/ChineseBert/ChineseBERT-base')
    #text='我爱你，中国'
    text='因为从你到台湾留学的那天至今已过了半年我们就没机会像之前能经常见面的聊东聊西。'
    encoded=a.tokenizer.encode(text)  # 已经通过encode方法进行了分词
    print("encoded: ", encoded.tokens)
    print(a.convert_sentence_to_pinyin_ids(text,encoded))
    print(a.convert_sentence_to_shengmu_yunmu_shengdiao_ids(text,encoded))
    '''