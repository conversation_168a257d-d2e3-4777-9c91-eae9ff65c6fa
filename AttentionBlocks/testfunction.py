"""


# 1-测试解析路径是否成功
import argparse


def get_parser():
    parser = argparse.ArgumentParser(description="Your script description")
    parser.add_argument("--data_dir", type=str, help="Path to the data directory")
    return parser


def main():
    parser = get_parser()
    args = parser.parse_args()

    # 指定 --data_dir 参数为具体某个路径
    args.data_dir = "E:/Code/SCOPE-test/dataprocess"

    print(f"Data directory: {args.data_dir}")


if __name__ == "__main__":
    main()

"""

"""
# 2-测试BertWordPieceTokenizer的分词
from tokenizers.implementations.bert_wordpiece import BertWordPieceTokenizer

# 初始化 tokenizer
tokenizer = BertWordPieceTokenizer(
    vocab="E:/Code/SCOPE-test/FPT/vocab.txt",  # 替换为你的词表文件路径
    # lowercase=True  # 根据需要设置是否转换为小写
)

# 编码文本
encoded = tokenizer.encode("Hello, how are you?")
print(encoded.tokens)
print(encoded.tokens[1])
"""

# 读取npy文件内容，华文仿宋、华文行楷、方正古隶繁体均为（23236, 24, 24）维的张量，即用24x24表示一个字的形状
import numpy as np
import os

file_path = 'E:/Code/SCOPE-main/FPT/config/方正古隶繁体.ttf24.npy'

# 检查文件是否存在
if os.path.exists(file_path):
    try:
        # 尝试加载 .npy 文件
        data = np.load(file_path)
        # 将数据转换为字符串格式
        data_str = np.array2string(data, separator=', ')
        # 定义输出文本文件名
        text_file_name = file_path.replace('.npy', '.txt')

        # 将数据写入文本文件
        with open(text_file_name, 'w', encoding='utf-8') as text_file:
            text_file.write(data_str)

        print(f"Data from {file_path} has been saved to {text_file_name}")
        # 打印文件内容
        print(data.shape)
    except Exception as e:
        # 捕获并打印异常
        print(f"Error loading the file: {e}")
else:
    print(f"File {file_path} does not exist.")
