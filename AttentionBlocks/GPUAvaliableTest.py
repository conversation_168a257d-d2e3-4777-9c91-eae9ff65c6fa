import json

import torch
import pytorch_lightning as pl

# 检查CUDA是否可用
cuda_available = torch.cuda.is_available()
print("CUDA是否可用： ", cuda_available)

print("pytorch_lightning版本：", pl.__version__)

if cuda_available:
    # 获取GPU设备数量
    num_gpu = torch.cuda.device_count()

    # 获取当前使用的GPU索引
    current_gpu_index = torch.cuda.current_device()

    # 获取当前GPU的名称
    current_gpu_name = torch.cuda.get_device_name(current_gpu_index)

    # 获取GPU显存的总量和已使用量
    total_memory = torch.cuda.get_device_properties(current_gpu_index).total_memory / (1024 ** 3)  # 显存总量(GB)
    used_memory = torch.cuda.memory_allocated(current_gpu_index) / (1024 ** 3)  # 已使用显存(GB)
    free_memory = total_memory - used_memory  # 剩余显存(GB)

    print(f"CUDA可用，共有 {num_gpu} 个GPU设备可用。")
    print(f"当前使用的GPU设备索引：{current_gpu_index}")
    print(f"当前使用的GPU设备名称：{current_gpu_name}")
    print(f"GPU显存总量：{total_memory:.2f} GB")
    print(f"已使用的GPU显存：{used_memory:.2f} GB")
    print(f"剩余GPU显存：{free_memory:.2f} GB")
else:
    print("CUDA不可用。")

# 检查PyTorch版本
print(f"PyTorch版本：{torch.__version__}")

import torch
print(f"CUDA版本：{torch.version.cuda}")

source_path = 'E:/Code/SCOPE-test/dataprocess/train_all'
target_path = 'E:/Code/SCOPE-test/dataprocess/testcontext.txt'

with open(source_path, 'r', encoding='utf-8', errors='ignore') as source_file, open(target_path, 'w', encoding='utf-8') as target_file:
    # 逐行从源文件中读取内容
    for line in source_file:
        # 并写入目标文件
        target_file.write(line)


