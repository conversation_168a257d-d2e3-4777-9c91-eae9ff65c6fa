'''
import torch
import torch.nn as nn


class Autoencoder(nn.Module):
    def __init__(self):
        super(Autoencoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(768, 512),  # 压缩到512维
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Linear(512, 768),  # 解压回768维
            nn.ReLU()
        )

    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x


# 创建模型
autoencoder = Autoencoder()

# 将768维向量转换为512维
input_vector = torch.rand(8, 30, 768)  # 假设输入一个批次，batch_size=1
encoded_vector = autoencoder.encoder(input_vector)
print(encoded_vector.shape)  # 输出: torch.Size([1, 512])
'''
from transformers import BertModel, BertTokenizer
import torch

# 1. 初始化模型和标记器
model_path = 'E:/Code/ChineseBert/ChineseBERT-base'
tokenizer = BertTokenizer.from_pretrained(model_path)
model = BertModel.from_pretrained(model_path)

# 通过input_ids的方式获取向量
input_ids = torch.tensor(tokenizer.encode("你好吗？", add_special_tokens=True)).unsqueeze(0)
print("input_ids.size(): ", input_ids.size())
print("input_ids: ", input_ids)
outputs_ids = model(input_ids)
#print("outputs_ids.shape:  ", outputs_ids[0].shape) # outputs_ids.shape:   torch.Size([1, 6, 768])
print("output_ids: ", outputs_ids[0])

# 2. 输入句子
#sentence = "你好吗?"  # [CLS] 你 好 吗 ？ [SEP]
sentence = "你在吃饭吗？"

# 3. 标记化输入句子
inputs = tokenizer(sentence, return_tensors='pt', padding=True, truncation=True)

# 4. 获取模型输出
with torch.no_grad():
    outputs = model(**inputs)

# 5. 获取词向量
# outputs.last_hidden_state: (batch_size, sequence_length, hidden_size)
# 在这里，batch_size=1, sequence_length=输入标记的数量, hidden_size=768 (对于BERT base模型)
word_vectors = outputs.last_hidden_state

# 6. 输出每个词的词向量
#for i, token in enumerate(tokenizer.convert_ids_to_tokens(inputs['input_ids'][0])):
    #print("vector.shape: ", word_vectors[0].shape)  #vector.shape: torch.Size([6, 768])
    #print(f"Token: {token}, Vector: {word_vectors[0][i]}")

