import torch

# 测试模块的缝合
# 在模型结构定义文件中进行缝合，而不是训练或者测试文件中
# 缝合时主要注意维度的衔接，即输入向量的维度与模块的通道数相匹配
"""
将模块复制到模型文件中后，只需要修改两个地方：
1. def __init__ 模型初始化
2. forward 前向传播
如何确定要衔接的维度？
在forward方法的输入中打印一下输入的维度
"""

if __name__ == '__main__':
    input = torch.randn(50, 32, 7, 7) # 随机生成一个特征图
    #se = SEAttention(channel = 32, reduction = 8) # 实例化SE模块，设置降维比率为8
    #output = se(input) # 将输入特征图通过SE模块进行处理
    #print(output.shape) # 打印处理后的特征图形状，验证某个模块的作用


"""
CSC模型结构：

CSCTask(
  (model): Dynamic_GlyceBertForMultiTask(
    (bert): GlyceBertModel(
    
      (embeddings): FusionBertEmbeddings(
        (word_embeddings): Embedding(23236, 768, padding_idx=0)  #23236x768
        (position_embeddings): Embedding(512, 768)                #521x768
        (token_type_embeddings): Embedding(2, 768)              #2x768
        (pinyin_embeddings): PinyinEmbedding(                  #32x768
          (embedding): Embedding(32, 128)
          (conv): Conv1d(128, 768, kernel_size=(2,), stride=(1,))
        )
        (glyph_embeddings): GlyphEmbedding(                   #23236x768
          (embedding): Embedding(23236, 1728)    #1728=24*24*3，glyph embedding
        )
        (glyph_map): Linear(in_features=1728, out_features=768, bias=True) # 1728降维到768，方便concat
        (map_fc): Linear(in_features=2304, out_features=768, bias=True)  # concat: 2304=768*3
        (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
        (dropout): Dropout(p=0.1, inplace=False)   #对输入随机失活，将部分元素变为0
      )
      
      (encoder): BertEncoder(
        (layer): ModuleList(
          (0-11): 12 x BertLayer(
            (attention): BertAttention(
              (self): BertSelfAttention(
                (query): Linear(in_features=768, out_features=768, bias=True)
                (key): Linear(in_features=768, out_features=768, bias=True)
                (value): Linear(in_features=768, out_features=768, bias=True)
                (dropout): Dropout(p=0.1, inplace=False)
              )
              (output): BertSelfOutput(
                (dense): Linear(in_features=768, out_features=768, bias=True)
                (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
                (dropout): Dropout(p=0.1, inplace=False)
              )
            )
            (intermediate): BertIntermediate(
              (dense): Linear(in_features=768, out_features=3072, bias=True)
            )
            (output): BertOutput(
              (dense): Linear(in_features=3072, out_features=768, bias=True)
              (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
              (dropout): Dropout(p=0.1, inplace=False)
            )
          )
        )
      )
      
      (pooler): BertPooler(                           768x768
        (dense): Linear(in_features=768, out_features=768, bias=True)
        (activation): Tanh()
      )
    )
    (cls): MultiTaskHeads(
      (predictions): BertLMPredictionHead(
        (transform): BertPredictionHeadTransform(
          (dense): Linear(in_features=768, out_features=768, bias=True)
          (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
        )
        (decoder): Linear(in_features=768, out_features=23236, bias=True)
      )
      (Phonetic_relationship): Phonetic_Classifier(
        (transform): BertPredictionHeadTransform(
          (dense): Linear(in_features=768, out_features=768, bias=True)
          (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)
        )
        (sm_classifier): Linear(in_features=768, out_features=24, bias=True)
        (ym_classifier): Linear(in_features=768, out_features=35, bias=True)
        (sd_classifier): Linear(in_features=768, out_features=6, bias=True)
      )
    )
    (loss_fct): CrossEntropyLoss()
  )
  (loss_fct): CrossEntropyLoss()
)
"""