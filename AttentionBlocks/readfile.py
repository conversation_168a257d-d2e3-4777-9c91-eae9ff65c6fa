
source_path = 'E:/Code/SCOPE-test/dataprocess/train_all'
#source_path = 'E:/Code/SCOPE-test/FPT/config/STFANGSO.TTF24.npy'
target_path = 'E:/Code/SCOPE-test/dataprocess/testcontext_2.txt'


import re

'''
def count_word_in_file(filename, word):
    # 打开文件，读取内容，然后关闭文件
    with open(filename, 'r', encoding='utf-8') as file:
        # 读取整个文件内容
        content = file.read()

    # 使用正则表达式分割所有单词，\b 是单词边界，\w+ 是一个或多个字母或数字
    words = re.findall(r'\b\w+\b', content)

    # 统计特定单词的数量，忽略大小写
    word_count = sum(1 for w in words if w.lower() == word.lower())

    # 返回特定单词的出现次数
    return word_count

# 指定文件名和要统计的词
word_to_count = 'input_ids'

# 调用函数并打印结果
count = count_word_in_file(target_path, word_to_count)
print(f"The word '{word_to_count}' appears {count} times in the file.")
'''

import numpy as np


def read_npy_file_first_10_elements(filename):
    # 加载npy文件
    data = np.load(filename)
    # 展平数组为一维
    flattened_data = data.flatten()

    # 获取前10个元素，如果元素不足10个，则获取所有元素
    first_elements = flattened_data[:100]

    return first_elements


# 指定npy文件名
filename = 'E:/Code/SCOPE-test/FPT/config/方正古隶繁体.ttf24.npy'

# 读取前10个元素
first_elements = read_npy_file_first_10_elements(filename)

# 打印前10个元素
print(first_elements)