<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="tqdm" />
            <item index="1" class="java.lang.String" itemvalue="pandas" />
            <item index="2" class="java.lang.String" itemvalue="openccpy" />
            <item index="3" class="java.lang.String" itemvalue="tensorflow" />
            <item index="4" class="java.lang.String" itemvalue="jieba" />
            <item index="5" class="java.lang.String" itemvalue="jieba-fast" />
            <item index="6" class="java.lang.String" itemvalue="plyvel" />
            <item index="7" class="java.lang.String" itemvalue="Levenshtein" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="bert4keras" />
            <item index="10" class="java.lang.String" itemvalue="openpyxl" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>