#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import os
from typing import List

import tokenizers
from pypinyin import pinyin, Style
from tokenizers.implementations.bert_wordpiece import BertWordPieceTokenizer
from torch.utils.data import Dataset

from datasets.utils import pho_convertor


class ChineseBertDataset(Dataset):

    def __init__(self, data_path, chinese_bert_path, max_length: int = 512):
        """
        Dataset Base class
        Args:
            data_path: dataset file path
            chinese_bert_path: pretrain model path
            max_length: max sentence length
        """
        super().__init__()
        self.vocab_file = os.path.join(chinese_bert_path, 'vocab.txt')
        self.config_path = os.path.join(chinese_bert_path, 'config')
        self.data_path = data_path
        self.max_length = max_length
        self.tokenizer = BertWordPieceTokenizer(self.vocab_file)
        # load pinyin map dict
        with open(os.path.join(self.config_path, 'pinyin_map.json'), encoding='utf8') as fin:
            self.pinyin_dict = json.load(fin)
        # load char id map tensor
        with open(os.path.join(self.config_path, 'id2pinyin.json'), encoding='utf8') as fin:
            self.id2pinyin = json.load(fin)
        # load pinyin map tensor
        with open(os.path.join(self.config_path, 'pinyin2tensor.json'), encoding='utf8') as fin:
            self.pinyin2tensor = json.load(fin)

        # self.lines = self.get_lines()

    @property
    def get_lines(self):
        """read data lines"""
        raise NotImplementedError

    def convert_sentence_to_pinyin_ids(self, sentence: str, tokenizer_output: tokenizers.Encoding) -> List[List[int]]:
        # get pinyin of a sentence
        pinyin_list = pinyin(sentence, style=Style.TONE3, heteronym=True, errors=lambda x: [['not chinese'] for _ in x])
        pinyin_locs = {}
        # get pinyin of each location
        for index, item in enumerate(pinyin_list):
            pinyin_string = item[0]
            # not a Chinese character, pass
            if pinyin_string == "not chinese":
                continue
            if pinyin_string in self.pinyin2tensor:
                pinyin_locs[index] = self.pinyin2tensor[pinyin_string]
            else:
                ids = [0] * 8
                for i, p in enumerate(pinyin_string):
                    if p not in self.pinyin_dict["char2idx"]:
                        ids = [0] * 8
                        break
                    ids[i] = self.pinyin_dict["char2idx"][p]
                pinyin_locs[index] = ids

        # find chinese character location, and generate pinyin ids
        pinyin_ids = []
        for idx, (token, offset) in enumerate(zip(tokenizer_output.tokens, tokenizer_output.offsets)):
            if offset[1] - offset[0] != 1:
                pinyin_ids.append([0] * 8)
                continue
            if offset[0] in pinyin_locs:
                pinyin_ids.append(pinyin_locs[offset[0]])
            else:
                pinyin_ids.append([0] * 8)

        return pinyin_ids
    def convert_sentence_to_shengmu_yunmu_shengdiao_ids(self, sentence: str, tokenizer_output: tokenizers.Encoding) -> List[List[int]]:
        # get pinyin of a sentence
        pinyin_list = pinyin(sentence, style=Style.TONE3, neutral_tone_with_five=True,heteronym=True, errors=lambda x: [['not chinese'] for _ in x])
        pinyin_locs = {}
        # get pinyin of each location
        for index, item in enumerate(pinyin_list):
            pinyin_string = item[0]
            # not a Chinese character, pass
            if pinyin_string == "not chinese":
                continue
            pinyin_locs[index] = pho_convertor.get_sm_ym_sd_labels(pinyin_string)

        # find chinese character location, and generate pinyin ids
        pinyin_labels = []
        for idx, (token, offset) in enumerate(zip(tokenizer_output.tokens, tokenizer_output.offsets)):
            if offset[1] - offset[0] != 1:
                pinyin_labels.append((0,0,0))
                continue
            if offset[0] in pinyin_locs:
                pinyin_labels.append(pinyin_locs[offset[0]])
            else:
                pinyin_labels.append((0,0,0))

        return pinyin_labels


if __name__=='__main__':
    a=ChineseBertDataset(None,'E:/Code/ChineseBert/ChineseBERT-base')
    #text='我爱你，中国'
    text='因为从你到台湾留学的那天至今已过了半年我们就没机会像之前能经常见面的聊东聊西。'
    encoded=a.tokenizer.encode(text)  # 已经通过encode方法进行了分词
    print("encoded: ", encoded.tokens)
    print(a.convert_sentence_to_pinyin_ids(text,encoded))
    print(a.convert_sentence_to_shengmu_yunmu_shengdiao_ids(text,encoded))